# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg

# Virtual environments
venv/
env/
ENV/

# IDE
.vscode/
.idea/
*.swp
*.swo
*~

# OS
.DS_Store
Thumbs.db

# Git
.git/
.gitignore

# Docker
Dockerfile*
docker-compose*
.dockerignore

# Logs
*.log
logs/

# Database (keep structure, not data)
*.db-journal

# Uploads (will be mounted as volume)
# uploads/

# Test files
test_*.py
*_test.py

# Documentation
*.md
docs/

# Cache
.cache/
.pytest_cache/

# 🚀 Complete Deployment Guide - Receipt Processing System

This comprehensive guide covers all deployment options for the Receipt Processing System, from local development to production deployment using Docker.

## 📋 Table of Contents

1. [Prerequisites](#prerequisites)
2. [Local Development (Without Docker)](#local-development-without-docker)
3. [Docker Deployment](#docker-deployment)
4. [Production Deployment](#production-deployment)
5. [Cloud Deployment](#cloud-deployment)
6. [Troubleshooting](#troubleshooting)

## Prerequisites

### System Requirements
- **Python 3.11+** (for local development)
- **Docker & Docker Compose** (for containerized deployment)
- **Tesseract OCR** (for local development, included in Docker)
- **Git** (to clone the repository)

### Install Prerequisites

#### Windows
```powershell
# Install Python from python.org
# Install Docker Desktop from docker.com
# Install Git from git-scm.com

# Install Tesseract (for local development)
# Download from: https://github.com/UB-Mannheim/tesseract/wiki
```

#### macOS
```bash
# Install Homebrew if not already installed
/bin/bash -c "$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)"

# Install dependencies
brew install python@3.11 docker git tesseract
```

#### Linux (Ubuntu/Debian)
```bash
# Update package list
sudo apt update

# Install dependencies
sudo apt install python3.11 python3.11-venv python3-pip git tesseract-ocr docker.io docker-compose

# Start Docker
sudo systemctl start docker
sudo systemctl enable docker
sudo usermod -aG docker $USER  # Add user to docker group
```

## Local Development (Without Docker)

### 1. Clone Repository
```bash
git clone <your-repo-url>
cd receipt-processing-system
```

### 2. Create Virtual Environment
```bash
# Windows
python -m venv venv
venv\Scripts\activate

# Linux/Mac
python3 -m venv venv
source venv/bin/activate
```

### 3. Install Dependencies
```bash
pip install --upgrade pip
pip install -r requirements.txt
```

### 4. Setup Database
```bash
python setup_database.py
```

### 5. Configure Tesseract (Windows Only)
Edit `backend/ocr_utils.py` and uncomment line 17:
```python
pytesseract.pytesseract.tesseract_cmd = r'C:\Program Files\Tesseract-OCR\tesseract.exe'
```

### 6. Run the Application

#### Option A: Run Both Services Separately
```bash
# Terminal 1 - Backend
python run_backend.py

# Terminal 2 - Frontend
python run_frontend.py
```

#### Option B: Use the Run Scripts
```bash
# Windows
python run_backend.py
python run_frontend.py

# Or use the provided scripts
```

### 7. Access the Application
- **Frontend**: http://localhost:8501
- **Backend API**: http://localhost:8000
- **API Docs**: http://localhost:8000/docs

## Docker Deployment

### Quick Start with Scripts

#### Windows
```cmd
# Start development environment
start.bat dev

# Start production environment
start.bat prod

# Stop services
start.bat stop

# View logs
start.bat logs

# Clean everything
start.bat clean
```

#### Linux/Mac
```bash
# Make script executable
chmod +x start.sh

# Start development environment
./start.sh dev

# Start production environment
./start.sh prod

# Stop services
./start.sh stop

# View logs
./start.sh logs

# Clean everything
./start.sh clean
```

### Manual Docker Commands

#### Development Environment
```bash
# Build and start
docker-compose up --build -d

# View logs
docker-compose logs -f

# Stop
docker-compose down
```

#### Production Environment
```bash
# Build and start
docker-compose -f docker-compose.prod.yml up --build -d

# View logs
docker-compose -f docker-compose.prod.yml logs -f

# Stop
docker-compose -f docker-compose.prod.yml down
```

### Access Points

#### Development Mode
- **Frontend**: http://localhost:8501
- **Backend**: http://localhost:8000
- **API Docs**: http://localhost:8000/docs

#### Production Mode (with Nginx)
- **Application**: http://localhost
- **API**: http://localhost/api/
- **Direct Frontend**: http://localhost:8501
- **Direct Backend**: http://localhost:8000

## Production Deployment

### 1. Environment Configuration
Create `.env` file:
```env
# Production settings
ENVIRONMENT=production
API_HOST=0.0.0.0
API_PORT=8000
FRONTEND_HOST=0.0.0.0
FRONTEND_PORT=8501

# Security
SECRET_KEY=your-secret-key-here
ALLOWED_ORIGINS=https://yourdomain.com

# Database
DATABASE_URL=sqlite:///./backend/receipts.db
```

### 2. SSL Configuration (Optional)
```bash
# Create SSL directory
mkdir ssl

# Add your SSL certificates
cp your-cert.pem ssl/cert.pem
cp your-key.pem ssl/key.pem

# Update nginx.prod.conf to enable HTTPS
```

### 3. Deploy with Production Settings
```bash
# Using startup script
./start.sh prod

# Or manually
docker-compose -f docker-compose.prod.yml up --build -d
```

### 4. Monitoring and Maintenance
```bash
# Check service health
docker-compose -f docker-compose.prod.yml ps

# View logs
docker-compose -f docker-compose.prod.yml logs -f

# Backup data
./start.sh backup

# Update application
git pull
docker-compose -f docker-compose.prod.yml up --build -d
```

## Cloud Deployment

### AWS ECS
```bash
# Build and push to ECR
aws ecr get-login-password --region us-west-2 | docker login --username AWS --password-stdin <account>.dkr.ecr.us-west-2.amazonaws.com

docker build -t receipt-processor .
docker tag receipt-processor:latest <account>.dkr.ecr.us-west-2.amazonaws.com/receipt-processor:latest
docker push <account>.dkr.ecr.us-west-2.amazonaws.com/receipt-processor:latest

# Deploy using ECS task definition
```

### Google Cloud Run
```bash
# Build and deploy
gcloud builds submit --tag gcr.io/PROJECT-ID/receipt-processor
gcloud run deploy receipt-processor \
  --image gcr.io/PROJECT-ID/receipt-processor \
  --platform managed \
  --region us-central1 \
  --allow-unauthenticated \
  --port 8000
```

### Heroku
```bash
# Login and create app
heroku login
heroku create your-app-name

# Deploy using container
heroku container:login
heroku container:push web -a your-app-name
heroku container:release web -a your-app-name
```

### DigitalOcean App Platform
```yaml
# app.yaml
name: receipt-processor
services:
- name: web
  source_dir: /
  github:
    repo: your-username/receipt-processor
    branch: main
  run_command: python run_backend.py
  environment_slug: python
  instance_count: 1
  instance_size_slug: basic-xxs
  http_port: 8000
```

## Troubleshooting

### Common Issues

#### 1. Port Already in Use
```bash
# Find and kill process using port
# Windows
netstat -ano | findstr :8000
taskkill /PID <PID> /F

# Linux/Mac
lsof -i :8000
kill -9 <PID>
```

#### 2. Docker Permission Issues (Linux)
```bash
sudo usermod -aG docker $USER
# Log out and back in
```

#### 3. Tesseract Not Found
```bash
# Check if tesseract is installed
tesseract --version

# Install if missing (see prerequisites section)
```

#### 4. Memory Issues
```bash
# Increase Docker memory in Docker Desktop settings
# Or add memory limits to docker-compose.yml
```

#### 5. Database Issues
```bash
# Reset database
rm backend/receipts.db
python setup_database.py
```

### Debug Mode
```bash
# Run with debug output
docker-compose up --build

# Access container shell
docker exec -it receipt-backend bash
docker exec -it receipt-frontend bash

# Check container logs
docker logs receipt-backend
docker logs receipt-frontend
```

### Performance Optimization
```bash
# Monitor resource usage
docker stats

# Scale services
docker-compose up --scale backend=3

# Use production WSGI server
# (Already configured in production docker-compose)
```

## Support and Maintenance

### Regular Maintenance
1. **Update dependencies**: `pip install -r requirements.txt --upgrade`
2. **Backup data**: `./start.sh backup`
3. **Monitor logs**: `./start.sh logs-f`
4. **Check health**: `./start.sh status`

### Getting Help
1. Check logs for error messages
2. Verify all services are running
3. Test individual components
4. Check system resources (CPU, memory, disk)
5. Consult the troubleshooting section above

For additional support, refer to the project documentation or create an issue in the repository.

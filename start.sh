#!/bin/bash

# Receipt Processing System - Docker Startup Script
# This script provides easy commands to manage the Docker deployment

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to check if Docker is running
check_docker() {
    if ! docker info > /dev/null 2>&1; then
        print_error "Docker is not running. Please start Docker and try again."
        exit 1
    fi
}

# Function to check if Docker Compose is available
check_docker_compose() {
    if ! command -v docker-compose > /dev/null 2>&1; then
        print_error "Docker Compose is not installed. Please install Docker Compose and try again."
        exit 1
    fi
}

# Function to show usage
show_usage() {
    echo "Receipt Processing System - Docker Management Script"
    echo ""
    echo "Usage: $0 [COMMAND]"
    echo ""
    echo "Commands:"
    echo "  dev         Start development environment"
    echo "  prod        Start production environment"
    echo "  stop        Stop all services"
    echo "  restart     Restart all services"
    echo "  logs        Show logs from all services"
    echo "  logs-f      Follow logs from all services"
    echo "  status      Show status of all services"
    echo "  clean       Stop and remove all containers, networks, and volumes"
    echo "  build       Build/rebuild all images"
    echo "  backup      Backup application data"
    echo "  restore     Restore application data from backup"
    echo "  help        Show this help message"
    echo ""
    echo "Examples:"
    echo "  $0 dev              # Start development environment"
    echo "  $0 prod             # Start production environment"
    echo "  $0 logs backend     # Show logs for backend service"
    echo "  $0 stop             # Stop all services"
}

# Function to start development environment
start_dev() {
    print_status "Starting development environment..."
    check_docker
    check_docker_compose
    
    docker-compose up --build -d
    
    print_success "Development environment started!"
    print_status "Services available at:"
    echo "  - Frontend (Streamlit): http://localhost:8501"
    echo "  - Backend API: http://localhost:8000"
    echo "  - API Documentation: http://localhost:8000/docs"
}

# Function to start production environment
start_prod() {
    print_status "Starting production environment..."
    check_docker
    check_docker_compose
    
    docker-compose -f docker-compose.prod.yml up --build -d
    
    print_success "Production environment started!"
    print_status "Services available at:"
    echo "  - Application: http://localhost"
    echo "  - Backend API: http://localhost/api/"
    echo "  - Direct Frontend: http://localhost:8501"
    echo "  - Direct Backend: http://localhost:8000"
}

# Function to stop services
stop_services() {
    print_status "Stopping all services..."
    
    if [ -f docker-compose.prod.yml ]; then
        docker-compose -f docker-compose.prod.yml down
    fi
    
    docker-compose down
    
    print_success "All services stopped!"
}

# Function to restart services
restart_services() {
    print_status "Restarting services..."
    stop_services
    sleep 2
    start_dev
}

# Function to show logs
show_logs() {
    if [ -n "$2" ]; then
        docker-compose logs "$2"
    else
        docker-compose logs
    fi
}

# Function to follow logs
follow_logs() {
    if [ -n "$2" ]; then
        docker-compose logs -f "$2"
    else
        docker-compose logs -f
    fi
}

# Function to show status
show_status() {
    print_status "Service status:"
    docker-compose ps
    
    print_status "Container health:"
    docker ps --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}"
}

# Function to clean everything
clean_all() {
    print_warning "This will remove all containers, networks, and volumes!"
    read -p "Are you sure? (y/N): " -n 1 -r
    echo
    
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        print_status "Cleaning up..."
        
        docker-compose -f docker-compose.prod.yml down -v --remove-orphans 2>/dev/null || true
        docker-compose down -v --remove-orphans
        
        # Remove images
        docker images | grep receipt | awk '{print $3}' | xargs docker rmi -f 2>/dev/null || true
        
        print_success "Cleanup completed!"
    else
        print_status "Cleanup cancelled."
    fi
}

# Function to build images
build_images() {
    print_status "Building/rebuilding all images..."
    docker-compose build --no-cache
    print_success "Images built successfully!"
}

# Function to backup data
backup_data() {
    print_status "Creating backup..."
    
    BACKUP_DIR="./backups"
    BACKUP_FILE="receipt-backup-$(date +%Y%m%d-%H%M%S).tar.gz"
    
    mkdir -p "$BACKUP_DIR"
    
    tar -czf "$BACKUP_DIR/$BACKUP_FILE" uploads/ backend/receipts.db 2>/dev/null || true
    
    print_success "Backup created: $BACKUP_DIR/$BACKUP_FILE"
}

# Function to restore data
restore_data() {
    print_status "Available backups:"
    ls -la ./backups/*.tar.gz 2>/dev/null || {
        print_error "No backups found in ./backups/"
        exit 1
    }
    
    read -p "Enter backup filename: " backup_file
    
    if [ -f "./backups/$backup_file" ]; then
        print_status "Restoring from $backup_file..."
        tar -xzf "./backups/$backup_file"
        print_success "Restore completed!"
    else
        print_error "Backup file not found!"
        exit 1
    fi
}

# Main script logic
case "$1" in
    "dev")
        start_dev
        ;;
    "prod")
        start_prod
        ;;
    "stop")
        stop_services
        ;;
    "restart")
        restart_services
        ;;
    "logs")
        show_logs "$@"
        ;;
    "logs-f")
        follow_logs "$@"
        ;;
    "status")
        show_status
        ;;
    "clean")
        clean_all
        ;;
    "build")
        build_images
        ;;
    "backup")
        backup_data
        ;;
    "restore")
        restore_data
        ;;
    "help"|"--help"|"-h")
        show_usage
        ;;
    "")
        show_usage
        ;;
    *)
        print_error "Unknown command: $1"
        echo ""
        show_usage
        exit 1
        ;;
esac

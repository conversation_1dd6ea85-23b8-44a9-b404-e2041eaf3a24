version: '3.8'

services:
  # Backend API Service (Production)
  backend:
    build: 
      context: .
      dockerfile: Dockerfile
    container_name: receipt-backend-prod
    ports:
      - "8000:8000"
    volumes:
      - uploads_data:/app/uploads
      - database_data:/app/backend
      - logs_data:/app/logs
    environment:
      - PYTHONPATH=/app
      - ENVIRONMENT=production
      - API_HOST=0.0.0.0
      - API_PORT=8000
    command: python run_backend.py
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 60s
    restart: always
    deploy:
      resources:
        limits:
          memory: 1G
          cpus: '0.5'
        reservations:
          memory: 512M
          cpus: '0.25'

  # Frontend Streamlit Service (Production)
  frontend:
    build: 
      context: .
      dockerfile: Dockerfile
    container_name: receipt-frontend-prod
    ports:
      - "8501:8501"
    environment:
      - PYTHONPATH=/app
      - ENVIRONMENT=production
      - FRONTEND_HOST=0.0.0.0
      - FRONTEND_PORT=8501
    command: python run_frontend.py
    depends_on:
      backend:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8501/"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 60s
    restart: always
    deploy:
      resources:
        limits:
          memory: 1G
          cpus: '0.5'
        reservations:
          memory: 512M
          cpus: '0.25'

  # Nginx reverse proxy (Production)
  nginx:
    image: nginx:alpine
    container_name: receipt-nginx-prod
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.prod.conf:/etc/nginx/nginx.conf:ro
      - ./ssl:/etc/nginx/ssl:ro  # For SSL certificates
    depends_on:
      - backend
      - frontend
    restart: always
    deploy:
      resources:
        limits:
          memory: 256M
          cpus: '0.25'

volumes:
  uploads_data:
    driver: local
  database_data:
    driver: local
  logs_data:
    driver: local

networks:
  default:
    driver: bridge

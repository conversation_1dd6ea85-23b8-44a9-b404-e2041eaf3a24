version: '3.8'

services:
  # Backend API Service
  backend:
    build: .
    container_name: receipt-backend
    ports:
      - "8000:8000"
    volumes:
      - ./uploads:/app/uploads
      - ./backend/receipts.db:/app/backend/receipts.db
    environment:
      - PYTHONPATH=/app
    command: python run_backend.py
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    restart: unless-stopped

  # Frontend Streamlit Service
  frontend:
    build: .
    container_name: receipt-frontend
    ports:
      - "8501:8501"
    environment:
      - PYTHONPATH=/app
    command: python run_frontend.py
    depends_on:
      backend:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8501/"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    restart: unless-stopped

  # Nginx reverse proxy (optional)
  nginx:
    image: nginx:alpine
    container_name: receipt-nginx
    ports:
      - "80:80"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf:ro
    depends_on:
      - backend
      - frontend
    restart: unless-stopped

volumes:
  uploads:
  database:

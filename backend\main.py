from fastapi import <PERSON><PERSON><PERSON>, File, UploadFile, Depends, HTTPException, status
from fastapi.middleware.cors import CORSMiddleware
from sqlalchemy.orm import Session
import os
import shutil
from typing import List
import PyPDF2
from io import BytesIO

from database import get_db, init_db, ReceiptFile, Receipt
from models import (
    ReceiptFileResponse, ReceiptResponse, UploadResponse, 
    ValidationResponse, ProcessResponse, ErrorResponse
)
from ocr_utils import ocr_processor

# Initialize FastAPI app
app = FastAPI(
    title="Receipt Processing API",
    description="API for processing scanned receipts with OCR",
    version="1.0.0"
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # In production, specify actual origins
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Initialize database on startup
@app.on_event("startup")
async def startup_event():
    init_db()
    # Create uploads directory if it doesn't exist
    os.makedirs("uploads", exist_ok=True)

@app.get("/")
async def root():
    """Root endpoint"""
    return {"message": "Receipt Processing API", "version": "1.0.0"}

@app.post("/upload", response_model=UploadResponse)
async def upload_receipt(
    file: UploadFile = File(...),
    db: Session = Depends(get_db)
):
    """Upload a receipt file (PDF format only)"""
    try:
        # Validate file type
        if not file.filename.lower().endswith('.pdf'):
            raise HTTPException(
                status_code=400,
                detail="Only PDF files are allowed"
            )
        
        # Check if file already exists (duplicate handling)
        existing_file = db.query(ReceiptFile).filter(
            ReceiptFile.file_name == file.filename
        ).first()
        
        if existing_file:
            # Update existing record instead of creating duplicate
            file_path = existing_file.file_path
            file_id = existing_file.id
            
            # Save the new file (overwrite)
            with open(file_path, "wb") as buffer:
                shutil.copyfileobj(file.file, buffer)
            
            # Reset processing status
            existing_file.is_valid = None
            existing_file.invalid_reason = None
            existing_file.is_processed = False
            db.commit()
            
            return UploadResponse(
                message="File updated successfully",
                file_id=file_id,
                file_name=file.filename,
                file_path=file_path
            )
        
        # Create new file record
        file_path = f"uploads/{file.filename}"
        
        # Save file to disk
        with open(file_path, "wb") as buffer:
            shutil.copyfileobj(file.file, buffer)
        
        # Create database record
        db_file = ReceiptFile(
            file_name=file.filename,
            file_path=file_path
        )
        db.add(db_file)
        db.commit()
        db.refresh(db_file)
        
        return UploadResponse(
            message="File uploaded successfully",
            file_id=db_file.id,
            file_name=file.filename,
            file_path=file_path
        )
        
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Error uploading file: {str(e)}"
        )

@app.post("/validate", response_model=ValidationResponse)
async def validate_receipt(
    file_id: int,
    db: Session = Depends(get_db)
):
    """Validate whether the uploaded file is a valid PDF"""
    try:
        # Get file record
        db_file = db.query(ReceiptFile).filter(ReceiptFile.id == file_id).first()
        if not db_file:
            raise HTTPException(status_code=404, detail="File not found")
        
        # Check if file exists on disk
        if not os.path.exists(db_file.file_path):
            db_file.is_valid = False
            db_file.invalid_reason = "File not found on disk"
            db.commit()
            
            return ValidationResponse(
                message="File validation failed",
                file_id=file_id,
                is_valid=False,
                invalid_reason="File not found on disk"
            )
        
        # Validate PDF
        try:
            with open(db_file.file_path, 'rb') as file:
                pdf_reader = PyPDF2.PdfReader(file)
                num_pages = len(pdf_reader.pages)
                
                if num_pages == 0:
                    raise Exception("PDF has no pages")
                
            # File is valid
            db_file.is_valid = True
            db_file.invalid_reason = None
            db.commit()
            
            return ValidationResponse(
                message="File is valid",
                file_id=file_id,
                is_valid=True
            )
            
        except Exception as pdf_error:
            # File is invalid
            db_file.is_valid = False
            db_file.invalid_reason = f"Invalid PDF: {str(pdf_error)}"
            db.commit()
            
            return ValidationResponse(
                message="File validation failed",
                file_id=file_id,
                is_valid=False,
                invalid_reason=f"Invalid PDF: {str(pdf_error)}"
            )
            
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Error validating file: {str(e)}"
        )

@app.post("/process", response_model=ProcessResponse)
async def process_receipt(
    file_id: int,
    db: Session = Depends(get_db)
):
    """Extract receipt details using OCR/AI and store in database"""
    try:
        # Get file record
        db_file = db.query(ReceiptFile).filter(ReceiptFile.id == file_id).first()
        if not db_file:
            raise HTTPException(status_code=404, detail="File not found")

        # Check if file is valid
        if db_file.is_valid is False:
            raise HTTPException(
                status_code=400,
                detail=f"Cannot process invalid file: {db_file.invalid_reason}"
            )

        # Check if file exists
        if not os.path.exists(db_file.file_path):
            raise HTTPException(status_code=404, detail="File not found on disk")

        # Process receipt with OCR
        try:
            extracted_data = ocr_processor.process_receipt(db_file.file_path)
        except Exception as ocr_error:
            raise HTTPException(
                status_code=500,
                detail=f"OCR processing failed: {str(ocr_error)}"
            )

        # Check if receipt already exists (for duplicate handling)
        existing_receipt = db.query(Receipt).filter(
            Receipt.file_path == db_file.file_path
        ).first()

        if existing_receipt:
            # Update existing receipt
            for key, value in extracted_data.items():
                if hasattr(existing_receipt, key):
                    setattr(existing_receipt, key, value)
            db.commit()
            db.refresh(existing_receipt)
            receipt_id = existing_receipt.id
        else:
            # Create new receipt record
            db_receipt = Receipt(
                file_path=db_file.file_path,
                **extracted_data
            )
            db.add(db_receipt)
            db.commit()
            db.refresh(db_receipt)
            receipt_id = db_receipt.id

        # Mark file as processed
        db_file.is_processed = True
        db.commit()

        # Get the receipt for response
        receipt = db.query(Receipt).filter(Receipt.id == receipt_id).first()

        return ProcessResponse(
            message="Receipt processed successfully",
            file_id=file_id,
            receipt_id=receipt_id,
            extracted_data=ReceiptResponse.from_orm(receipt)
        )

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Error processing receipt: {str(e)}"
        )

@app.get("/receipts", response_model=List[ReceiptResponse])
async def get_all_receipts(
    skip: int = 0,
    limit: int = 100,
    db: Session = Depends(get_db)
):
    """List all receipts stored in the database"""
    try:
        receipts = db.query(Receipt).offset(skip).limit(limit).all()
        return [ReceiptResponse.from_orm(receipt) for receipt in receipts]
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Error retrieving receipts: {str(e)}"
        )

@app.get("/receipts/{receipt_id}", response_model=ReceiptResponse)
async def get_receipt(
    receipt_id: int,
    db: Session = Depends(get_db)
):
    """Retrieve details of a specific receipt by its ID"""
    try:
        receipt = db.query(Receipt).filter(Receipt.id == receipt_id).first()
        if not receipt:
            raise HTTPException(status_code=404, detail="Receipt not found")

        return ReceiptResponse.from_orm(receipt)
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Error retrieving receipt: {str(e)}"
        )

@app.get("/files", response_model=List[ReceiptFileResponse])
async def get_all_files(
    skip: int = 0,
    limit: int = 100,
    db: Session = Depends(get_db)
):
    """List all uploaded files"""
    try:
        files = db.query(ReceiptFile).offset(skip).limit(limit).all()
        return [ReceiptFileResponse.from_orm(file) for file in files]
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Error retrieving files: {str(e)}"
        )

@app.get("/files/{file_id}", response_model=ReceiptFileResponse)
async def get_file(
    file_id: int,
    db: Session = Depends(get_db)
):
    """Retrieve details of a specific file by its ID"""
    try:
        file = db.query(ReceiptFile).filter(ReceiptFile.id == file_id).first()
        if not file:
            raise HTTPException(status_code=404, detail="File not found")

        return ReceiptFileResponse.from_orm(file)
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Error retrieving file: {str(e)}"
        )

@app.delete("/files/{file_id}")
async def delete_file(
    file_id: int,
    db: Session = Depends(get_db)
):
    """Delete a file and its associated receipt data"""
    try:
        # Get file record
        db_file = db.query(ReceiptFile).filter(ReceiptFile.id == file_id).first()
        if not db_file:
            raise HTTPException(status_code=404, detail="File not found")

        # Delete associated receipt data if exists
        receipt = db.query(Receipt).filter(Receipt.file_path == db_file.file_path).first()
        if receipt:
            db.delete(receipt)

        # Delete physical file if it exists
        if os.path.exists(db_file.file_path):
            os.remove(db_file.file_path)

        # Delete file record from database
        db.delete(db_file)
        db.commit()

        return {"message": "File deleted successfully", "file_id": file_id}

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Error deleting file: {str(e)}"
        )

@app.post("/upload-validate-process", response_model=ProcessResponse)
async def upload_validate_and_process(
    file: UploadFile = File(...),
    db: Session = Depends(get_db)
):
    """Combined endpoint: Upload, validate, and process a receipt file in one step"""
    try:
        # Step 1: Upload
        if not file.filename.lower().endswith('.pdf'):
            raise HTTPException(
                status_code=400,
                detail="Only PDF files are allowed"
            )

        # Check if file already exists (duplicate handling)
        existing_file = db.query(ReceiptFile).filter(
            ReceiptFile.file_name == file.filename
        ).first()

        if existing_file:
            # Update existing record instead of creating duplicate
            file_path = existing_file.file_path
            file_id = existing_file.id

            # Save the new file (overwrite)
            with open(file_path, "wb") as buffer:
                shutil.copyfileobj(file.file, buffer)

            # Reset processing status
            existing_file.is_valid = None
            existing_file.invalid_reason = None
            existing_file.is_processed = False
            db.commit()
        else:
            # Create new file record
            file_path = f"uploads/{file.filename}"

            # Save file to disk
            with open(file_path, "wb") as buffer:
                shutil.copyfileobj(file.file, buffer)

            # Create database record
            db_file = ReceiptFile(
                file_name=file.filename,
                file_path=file_path
            )
            db.add(db_file)
            db.commit()
            db.refresh(db_file)
            file_id = db_file.id

        # Step 2: Validate
        db_file = db.query(ReceiptFile).filter(ReceiptFile.id == file_id).first()

        try:
            with open(db_file.file_path, 'rb') as pdf_file:
                pdf_reader = PyPDF2.PdfReader(pdf_file)
                num_pages = len(pdf_reader.pages)

                if num_pages == 0:
                    raise Exception("PDF has no pages")

            # File is valid
            db_file.is_valid = True
            db_file.invalid_reason = None
            db.commit()

        except Exception as pdf_error:
            # File is invalid
            db_file.is_valid = False
            db_file.invalid_reason = f"Invalid PDF: {str(pdf_error)}"
            db.commit()

            raise HTTPException(
                status_code=400,
                detail=f"File validation failed: Invalid PDF: {str(pdf_error)}"
            )

        # Step 3: Process
        # Extract data using OCR
        extracted_data = ocr_processor.process_receipt(db_file.file_path)

        # Check if receipt already exists for this file
        existing_receipt = db.query(Receipt).filter(Receipt.file_path == db_file.file_path).first()

        if existing_receipt:
            # Update existing receipt
            for key, value in extracted_data.items():
                if hasattr(existing_receipt, key):
                    setattr(existing_receipt, key, value)
            db.commit()
            db.refresh(existing_receipt)
            receipt_id = existing_receipt.id
        else:
            # Create new receipt record
            db_receipt = Receipt(
                file_path=db_file.file_path,
                **extracted_data
            )
            db.add(db_receipt)
            db.commit()
            db.refresh(db_receipt)
            receipt_id = db_receipt.id

        # Mark file as processed
        db_file.is_processed = True
        db.commit()

        # Get the receipt for response
        receipt = db.query(Receipt).filter(Receipt.id == receipt_id).first()

        return ProcessResponse(
            message="File uploaded, validated, and processed successfully",
            file_id=file_id,
            receipt_id=receipt_id,
            extracted_data=ReceiptResponse.from_orm(receipt)
        )

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Error in upload-validate-process: {str(e)}"
        )

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)

@echo off
REM Receipt Processing System - Docker Startup Script for Windows
REM This script provides easy commands to manage the Docker deployment

setlocal enabledelayedexpansion

REM Function to print colored output (Windows doesn't support colors easily, so we'll use plain text)
:print_status
echo [INFO] %~1
goto :eof

:print_success
echo [SUCCESS] %~1
goto :eof

:print_warning
echo [WARNING] %~1
goto :eof

:print_error
echo [ERROR] %~1
goto :eof

REM Function to check if Dock<PERSON> is running
:check_docker
docker info >nul 2>&1
if errorlevel 1 (
    call :print_error "Docker is not running. Please start Docker Desktop and try again."
    exit /b 1
)
goto :eof

REM Function to check if Docker Compose is available
:check_docker_compose
docker-compose --version >nul 2>&1
if errorlevel 1 (
    call :print_error "Docker Compose is not installed. Please install Docker Compose and try again."
    exit /b 1
)
goto :eof

REM Function to show usage
:show_usage
echo Receipt Processing System - Docker Management Script
echo.
echo Usage: %~nx0 [COMMAND]
echo.
echo Commands:
echo   dev         Start development environment
echo   prod        Start production environment
echo   stop        Stop all services
echo   restart     Restart all services
echo   logs        Show logs from all services
echo   logs-f      Follow logs from all services
echo   status      Show status of all services
echo   clean       Stop and remove all containers, networks, and volumes
echo   build       Build/rebuild all images
echo   backup      Backup application data
echo   help        Show this help message
echo.
echo Examples:
echo   %~nx0 dev              # Start development environment
echo   %~nx0 prod             # Start production environment
echo   %~nx0 logs backend     # Show logs for backend service
echo   %~nx0 stop             # Stop all services
goto :eof

REM Function to start development environment
:start_dev
call :print_status "Starting development environment..."
call :check_docker
call :check_docker_compose

docker-compose up --build -d

call :print_success "Development environment started!"
call :print_status "Services available at:"
echo   - Frontend (Streamlit): http://localhost:8501
echo   - Backend API: http://localhost:8000
echo   - API Documentation: http://localhost:8000/docs
goto :eof

REM Function to start production environment
:start_prod
call :print_status "Starting production environment..."
call :check_docker
call :check_docker_compose

docker-compose -f docker-compose.prod.yml up --build -d

call :print_success "Production environment started!"
call :print_status "Services available at:"
echo   - Application: http://localhost
echo   - Backend API: http://localhost/api/
echo   - Direct Frontend: http://localhost:8501
echo   - Direct Backend: http://localhost:8000
goto :eof

REM Function to stop services
:stop_services
call :print_status "Stopping all services..."

if exist docker-compose.prod.yml (
    docker-compose -f docker-compose.prod.yml down
)

docker-compose down

call :print_success "All services stopped!"
goto :eof

REM Function to restart services
:restart_services
call :print_status "Restarting services..."
call :stop_services
timeout /t 2 /nobreak >nul
call :start_dev
goto :eof

REM Function to show logs
:show_logs
if "%~2"=="" (
    docker-compose logs
) else (
    docker-compose logs %~2
)
goto :eof

REM Function to follow logs
:follow_logs
if "%~2"=="" (
    docker-compose logs -f
) else (
    docker-compose logs -f %~2
)
goto :eof

REM Function to show status
:show_status
call :print_status "Service status:"
docker-compose ps

call :print_status "Container health:"
docker ps --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}"
goto :eof

REM Function to clean everything
:clean_all
call :print_warning "This will remove all containers, networks, and volumes!"
set /p confirm="Are you sure? (y/N): "

if /i "!confirm!"=="y" (
    call :print_status "Cleaning up..."
    
    docker-compose -f docker-compose.prod.yml down -v --remove-orphans 2>nul
    docker-compose down -v --remove-orphans
    
    call :print_success "Cleanup completed!"
) else (
    call :print_status "Cleanup cancelled."
)
goto :eof

REM Function to build images
:build_images
call :print_status "Building/rebuilding all images..."
docker-compose build --no-cache
call :print_success "Images built successfully!"
goto :eof

REM Function to backup data
:backup_data
call :print_status "Creating backup..."

set BACKUP_DIR=.\backups
set BACKUP_FILE=receipt-backup-%date:~-4,4%%date:~-10,2%%date:~-7,2%-%time:~0,2%%time:~3,2%%time:~6,2%.tar.gz

if not exist "%BACKUP_DIR%" mkdir "%BACKUP_DIR%"

REM Note: Windows tar might not be available on all systems
tar -czf "%BACKUP_DIR%\%BACKUP_FILE%" uploads\ backend\receipts.db 2>nul

call :print_success "Backup created: %BACKUP_DIR%\%BACKUP_FILE%"
goto :eof

REM Main script logic
if "%~1"=="dev" (
    call :start_dev
) else if "%~1"=="prod" (
    call :start_prod
) else if "%~1"=="stop" (
    call :stop_services
) else if "%~1"=="restart" (
    call :restart_services
) else if "%~1"=="logs" (
    call :show_logs %*
) else if "%~1"=="logs-f" (
    call :follow_logs %*
) else if "%~1"=="status" (
    call :show_status
) else if "%~1"=="clean" (
    call :clean_all
) else if "%~1"=="build" (
    call :build_images
) else if "%~1"=="backup" (
    call :backup_data
) else if "%~1"=="help" (
    call :show_usage
) else if "%~1"=="" (
    call :show_usage
) else (
    call :print_error "Unknown command: %~1"
    echo.
    call :show_usage
    exit /b 1
)

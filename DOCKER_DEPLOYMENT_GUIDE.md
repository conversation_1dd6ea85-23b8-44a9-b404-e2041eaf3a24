# 🐳 Docker Deployment Guide - Receipt Processing System

This guide covers how to run the Receipt Processing System using Docker for both local development and production deployment.

## 📋 Prerequisites

- **Docker**: Install Docker Desktop (Windows/Mac) or Docker Engine (Linux)
- **Docker Compose**: Usually included with Docker Desktop
- **Git**: To clone the repository

### Install Docker

#### Windows/Mac
1. Download Docker Desktop from https://www.docker.com/products/docker-desktop
2. Install and start Docker Desktop
3. Verify installation: `docker --version`

#### Linux (Ubuntu/Debian)
```bash
sudo apt update
sudo apt install docker.io docker-compose
sudo systemctl start docker
sudo systemctl enable docker
sudo usermod -aG docker $USER  # Add user to docker group
# Log out and back in for group changes to take effect
```

## 🚀 Quick Start (Local Development)

### Option 1: Using Startup Scripts (Recommended)

#### Windows
```cmd
# Start development environment
start.bat dev

# Stop all services
start.bat stop

# View logs
start.bat logs

# Get help
start.bat help
```

#### Linux/Mac
```bash
# Make script executable
chmod +x start.sh

# Start development environment
./start.sh dev

# Stop all services
./start.sh stop

# View logs
./start.sh logs

# Get help
./start.sh help
```

### Option 2: Direct Docker Compose

#### 1. Clone and Navigate
```bash
git clone <your-repo-url>
cd receipt-processing-system
```

#### 2. Build and Run with Docker Compose
```bash
# Build and start all services
docker-compose up --build

# Or run in background
docker-compose up --build -d
```

#### 3. Access the Application
- **Frontend (Streamlit)**: http://localhost:8501
- **Backend API**: http://localhost:8000
- **API Documentation**: http://localhost:8000/docs
- **With Nginx**: http://localhost (if nginx service is enabled)

#### 4. Stop the Application
```bash
# Stop all services
docker-compose down

# Stop and remove volumes (clears data)
docker-compose down -v
```

## 🔧 Configuration Options

### Environment Variables
Create a `.env` file in the project root:

```env
# API Configuration
API_HOST=0.0.0.0
API_PORT=8000

# Frontend Configuration
FRONTEND_HOST=0.0.0.0
FRONTEND_PORT=8501

# Database
DATABASE_URL=sqlite:///./backend/receipts.db

# OCR Configuration
TESSERACT_CMD=/usr/bin/tesseract
```

### Custom Docker Compose
For production, create `docker-compose.prod.yml`:

```yaml
version: '3.8'
services:
  backend:
    build: .
    ports:
      - "8000:8000"
    environment:
      - ENVIRONMENT=production
    volumes:
      - ./uploads:/app/uploads
      - ./data:/app/data
    restart: always

  frontend:
    build: .
    ports:
      - "8501:8501"
    environment:
      - ENVIRONMENT=production
    depends_on:
      - backend
    restart: always
```

Run with: `docker-compose -f docker-compose.prod.yml up -d`

## 🏗️ Manual Docker Build

### Build Individual Images
```bash
# Build the application image
docker build -t receipt-processor .

# Run backend only
docker run -d -p 8000:8000 --name receipt-backend receipt-processor python run_backend.py

# Run frontend only
docker run -d -p 8501:8501 --name receipt-frontend receipt-processor python run_frontend.py
```

## 📊 Monitoring and Logs

### View Logs
```bash
# All services
docker-compose logs

# Specific service
docker-compose logs backend
docker-compose logs frontend

# Follow logs in real-time
docker-compose logs -f backend
```

### Health Checks
```bash
# Check service status
docker-compose ps

# Check health of specific container
docker inspect --format='{{.State.Health.Status}}' receipt-backend
```

## 🔒 Production Deployment

### 1. Security Considerations
- Change default ports
- Use environment variables for sensitive data
- Enable HTTPS with SSL certificates
- Restrict CORS origins in backend
- Use secrets management

### 2. Performance Optimization
```dockerfile
# Multi-stage build for smaller images
FROM python:3.11-slim as builder
COPY requirements.txt .
RUN pip install --user -r requirements.txt

FROM python:3.11-slim
COPY --from=builder /root/.local /root/.local
COPY . .
CMD ["python", "run_backend.py"]
```

### 3. Persistent Data
```yaml
volumes:
  - ./data/uploads:/app/uploads
  - ./data/database:/app/backend
  - ./data/logs:/app/logs
```

## 🌐 Cloud Deployment

### AWS ECS
1. Push image to ECR
2. Create ECS task definition
3. Deploy to ECS cluster

### Google Cloud Run
```bash
# Build and push
docker build -t gcr.io/PROJECT-ID/receipt-processor .
docker push gcr.io/PROJECT-ID/receipt-processor

# Deploy
gcloud run deploy receipt-processor \
  --image gcr.io/PROJECT-ID/receipt-processor \
  --platform managed \
  --region us-central1 \
  --allow-unauthenticated
```

### Heroku
```bash
# Install Heroku CLI and login
heroku container:login

# Push and release
heroku container:push web -a your-app-name
heroku container:release web -a your-app-name
```

## 🐛 Troubleshooting

### Common Issues

#### 1. Port Already in Use
```bash
# Find process using port
lsof -i :8000  # or netstat -tulpn | grep 8000

# Kill process
kill -9 <PID>

# Or use different ports
docker-compose up --build -p 8001:8000
```

#### 2. Permission Denied (Linux)
```bash
# Add user to docker group
sudo usermod -aG docker $USER
# Log out and back in
```

#### 3. Tesseract Not Found
The Docker image includes Tesseract, but if you see errors:
```bash
# Check if tesseract is installed in container
docker exec -it receipt-backend tesseract --version
```

#### 4. Database Issues
```bash
# Reset database
docker-compose down -v
docker-compose up --build
```

#### 5. Memory Issues
```bash
# Increase Docker memory limit in Docker Desktop settings
# Or add to docker-compose.yml:
services:
  backend:
    mem_limit: 1g
    memswap_limit: 1g
```

### Debug Mode
```bash
# Run with debug output
docker-compose up --build --verbose

# Access container shell
docker exec -it receipt-backend bash
docker exec -it receipt-frontend bash
```

## 📈 Scaling

### Horizontal Scaling
```yaml
services:
  backend:
    deploy:
      replicas: 3
    ports:
      - "8000-8002:8000"
```

### Load Balancer
Use nginx or cloud load balancer to distribute traffic across multiple backend instances.

## 🔄 Updates and Maintenance

### Update Application
```bash
# Pull latest code
git pull

# Rebuild and restart
docker-compose down
docker-compose up --build -d
```

### Backup Data
```bash
# Backup uploads and database
docker run --rm -v $(pwd):/backup alpine tar czf /backup/backup.tar.gz /app/uploads /app/backend/receipts.db
```

## 📞 Support

If you encounter issues:
1. Check the logs: `docker-compose logs`
2. Verify all services are healthy: `docker-compose ps`
3. Test individual components
4. Check Docker and system resources

For more help, refer to the main README.md or create an issue in the repository.
